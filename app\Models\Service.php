<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Spatie\LaravelPackageTools\Concerns\Package\HasTranslations;

class Service extends Model
{
    use HasFactory , HasTranslations;

    protected $fillable = [
        'name',
        'price',
        'duration',
        'expected_time_to_accept',
        'description',
        'is_active',
        'provider_id',
        'category_id'
    ];

    public $translatable = ['name','description'];


    protected $casts = [
        'price' => 'decimal:2',
        'duration' => 'integer',
        'expected_time_to_accept' => 'integer',
        'is_active' => 'boolean',
    ];

    /**
     * Get the provider that owns the service
     */
    public function provider()
    {
        return $this->belongsTo(Provider::class);
    }

    /**
     * Get the category that owns the service
     */
    public function category()
    {
        return $this->belongsTo(Category::class);
    }

    /**
     * Scope a query to only include active services
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope a query to only include services for a specific provider
     */
    public function scopeForProvider($query, $providerId)
    {
        return $query->where('provider_id', $providerId);
    }

    /**
     * Get formatted duration in hours and minutes
     */
    public function getFormattedDurationAttribute()
    {
        $hours = intval($this->duration / 60);
        $minutes = $this->duration % 60;

        if ($hours > 0 && $minutes > 0) {
            return "{$hours}h {$minutes}m";
        } elseif ($hours > 0) {
            return "{$hours}h";
        } else {
            return "{$minutes}m";
        }
    }

    /**
     * Get formatted expected time to accept
     */
    public function getFormattedExpectedTimeAttribute()
    {
        if (!$this->expected_time_to_accept) {
            return null;
        }

        $hours = intval($this->expected_time_to_accept / 60);
        $minutes = $this->expected_time_to_accept % 60;

        if ($hours > 0 && $minutes > 0) {
            return "{$hours}h {$minutes}m";
        } elseif ($hours > 0) {
            return "{$hours}h";
        } else {
            return "{$minutes}m";
        }
    }
}
